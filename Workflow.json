{"name": "My workflow 2", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-5888, 2864], "id": "1d0e38c9-f314-4d92-933e-7e90212b465c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"url": "https://api.apify.com/v2/acts/compass~crawler-google-places/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"includeWebResults\": false,\n    \"language\": \"lt\",\n    \"locationQuery\": \"{{ $json.location }}\",\n    \"maxCrawledPlacesPerSearch\": 10,\n    \"maxImages\": 0,\n    \"maximumLeadsEnrichmentRecords\": 0,\n    \"scrapeContacts\": false,\n    \"scrapeDirectories\": false,\n    \"scrapeImageAuthors\": false,\n    \"scrapePlaceDetailPage\": false,\n    \"scrapeReviewsPersonalData\": true,\n    \"scrapeTableReservationProvider\": false,\n    \"searchStringsArray\": [\n        \"{{ $json.query }}\"\n    ],\n    \"skipClosedPlaces\": false\n}\n", "options": {}}, "id": "b0b83502-eeaa-4970-b5e6-c88366f389ca", "name": "Apify Google Maps Scraper3", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-5648, 2864]}, {"parameters": {"jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\nconst seenBusinesses = new Set(); // Track duplicates within this batch\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const businessName = data.title || data.name || 'Unknown';\n    const address = data.address || data.location || 'Unknown';\n    \n    // Create a unique key for deduplication (business name + address)\n    const uniqueKey = `${businessName.toLowerCase().trim()}_${address.toLowerCase().trim()}`;\n    \n    // Skip if we've already seen this business in this batch\n    if (seenBusinesses.has(uniqueKey)) {\n      console.log(`Skipping duplicate business: ${businessName}`);\n      continue;\n    }\n    \n    seenBusinesses.add(uniqueKey);\n    \n    const lead = {\n      business_name: businessName,\n      address: address,\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || '',\n      director_name: '', // Will be filled from rekvizitai\n      owner_email: '' // Will be filled from rekvizitai\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} unique businesses without websites (removed ${businesses.length - filteredLeads.length} duplicates)`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}, "id": "b4864440-637d-4685-9925-900a71d18c4d", "name": "Filter Businesses Without Website3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-5440, 2864]}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.address }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-4832, 2784], "id": "73e4c420-f651-42d4-9119-7ccf76e54691", "name": "Remove Duplicates3"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw", "mode": "list", "cachedResultName": "Leads_Unfiltered", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1p2OxinsL6Kx7Tgd4NQtlFjCSZ4PV1p-Mjo3GoezwXbw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone.replace('+', '') }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_status }}", "sms_status": "={{ $json.sms_status }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "fd467ddb-19db-4ae5-a02a-b5091c2ded16", "name": "Unfiltered_Leads3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-5008, 2864], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "reviews_count": "={{ $json.reviews_count }}", "rating": "={{ $json.rating }}", "category": "={{ $json.category }}", "google_maps_url": "={{ $json.google_maps_url }}", "notes": "={{ $json.notes }}", "reply_received": "={{ $json.reply_received }}", "sms_status": "={{ $json.sms_status }}", "sms_sent": "={{ $json.sms_sent }}", "phone_source": "={{ $json.phone_source }}", "owner_phone": "={{ $json.owner_phone }}", "website": "={{ $json.website }}", "google_phone": "={{ $json.google_phone}}"}, "matchingColumns": [], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "aac4034e-ea7d-450b-b20c-c8956e663d9b", "name": "Filtered_Leads3", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-4720, 3088], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "for (const item of $input.all()) {\n  // Normalize reviews_count\n  let count = item.json.reviews_count;\n  if (count === undefined || count === null || count === '') {\n    item.json.reviews_count = 0;\n  } else {\n    item.json.reviews_count = Number(count);\n  }\n\n  // Normalize rating\n  let rating = item.json.rating;\n  if (rating === undefined || rating === null || rating === '') {\n    item.json.rating = 0;\n  } else {\n    item.json.rating = Number(rating);\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-5232, 2864], "id": "cdbf157c-fe69-4769-b097-e5c746eeaaf2", "name": "Normalise reviews, and3"}, {"parameters": {"url": "https://app.sms8.io/services/send.php", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "dadea4239d37bc693307d751a7be0be596d80bed"}, {"name": "number", "value": "={{ ($json.google_phone || $json.owner_phone).replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"name": "message", "value": "=<PERSON><PERSON><PERSON>, <PERSON><PERSON> iš Upzera👋!\n\n<PERSON><PERSON> jauna komanda, pad<PERSON><PERSON> verslams sukurti svetaines ir pritraukti daugiau klientų pagerinant matomumą internetinėje erdvėje.\n\nNorėčiau trumpai pasidalinti idėja, kaip ne<PERSON>ti potencialių klientų – ar domintų sužinoti daugiau?\n\nupzera.com/lt"}, {"name": "device", "value": "[\"3771 | 0\"]"}, {"name": "type", "value": "sms"}, {"name": "prioritize", "value": "0"}]}, "options": {"response": {"response": {"responseFormat": "json", "outputPropertyName": "sms_response"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4160, 2736], "id": "397b0e59-e61a-487f-b4bc-15184fc4b84f", "name": "SMS11"}, {"parameters": {"jsCode": "// Process SMS response and prepare data for Google Sheets update\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  // Now the SMS response is in sms_response property, and original data is preserved\n  const originalData = item.json;\n  const smsResponse = item.json.sms_response || item.json; // Fallback to full json if sms_response not found\n  \n  // Determine SMS status based on response\n  let smsStatus = 'failed';\n  let smsStatusDetails = '';\n  \n  // Check if SMS was sent successfully\n  // SMS8 typically returns success: true/false or status codes\n  if (smsResponse.success === true || smsResponse.status === 'success' || smsResponse.code === 200) {\n    smsStatus = 'success';\n    smsStatusDetails = 'SMS sent successfully';\n  } else if (smsResponse.success === false || smsResponse.error) {\n    smsStatus = 'failed';\n    smsStatusDetails = smsResponse.error || smsResponse.message || 'SMS sending failed';\n  } else {\n    // If response structure is different, check for common success indicators\n    const responseStr = JSON.stringify(smsResponse).toLowerCase();\n    if (responseStr.includes('success') || responseStr.includes('sent')) {\n      smsStatus = 'success';\n      smsStatusDetails = 'SMS sent successfully';\n    } else {\n      smsStatus = 'failed';\n      smsStatusDetails = 'Unknown SMS response: ' + JSON.stringify(smsResponse);\n    }\n  }\n  \n  // Prepare updated data - now we have access to the original business data\n  const updatedData = {\n    business_name: originalData.business_name,\n    address: originalData.address,\n    google_phone: originalData.google_phone,\n    owner_phone: originalData.owner_phone,\n    website: originalData.website,\n    phone_source: originalData.phone_source,\n    reply_received: originalData.reply_received,\n    notes: originalData.notes,\n    google_maps_url: originalData.google_maps_url,\n    category: originalData.category,\n    rating: originalData.rating,\n    reviews_count: originalData.reviews_count,\n    sms_sent: smsStatus,\n    sms_status: smsStatusDetails,\n    sms_sent_date: new Date().toISOString().split('T')[0],\n    phone_used: originalData.google_phone || originalData.owner_phone || 'no_phone',\n    sms_response_raw: JSON.stringify(smsResponse) // Keep raw response for debugging\n  };\n  \n  processedItems.push({ json: updatedData });\n}\n\nconsole.log(`Processed ${processedItems.length} SMS responses`);\nreturn processedItems;"}, "id": "4a94da09-37ba-4113-bd86-283fe45c27a5", "name": "Process SMS Response2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4096, 3056]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "condition1", "leftValue": "={{ $json.sms_sent }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "78b46672-ca86-4d72-ac26-6417ad55c6bd", "name": "Check SMS Success2", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-3872, 2912]}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name || $json.title || 'Unknown Business' }}", "address": "={{ $json.address || '' }}", "google_phone": "={{ $json.google_phone || '' }}", "owner_phone": "={{ $json.owner_phone || '' }}", "website": "={{ $json.website || '' }}", "phone_source": "={{ $json.phone_source || '' }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received || 'no' }}", "notes": "={{ $json.notes || '' }}", "google_maps_url": "={{ $json.google_maps_url || '' }}", "category": "={{ $json.category || '' }}", "rating": "={{ $json.rating || '' }}", "reviews_count": "={{ $json.reviews_count || '' }}"}, "matchingColumns": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "e355cda6-0a23-4c04-8bb4-885ac5139150", "name": "Update SMS Success Status2", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3600, 2768], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name || $json.title || 'Unknown Business' }}", "address": "={{ $json.address || '' }}", "google_phone": "={{ $json.google_phone || '' }}", "owner_phone": "={{ $json.owner_phone || '' }}", "website": "={{ $json.website || '' }}", "phone_source": "={{ $json.phone_source || '' }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received || 'no' }}", "notes": "={{ $json.notes || '' }}", "google_maps_url": "={{ $json.google_maps_url || '' }}", "category": "={{ $json.category || '' }}", "rating": "={{ $json.rating || '' }}", "reviews_count": "={{ $json.reviews_count || '' }}"}, "matchingColumns": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "schema": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "owner_phone", "displayName": "owner_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone_source", "displayName": "phone_source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_sent", "displayName": "sms_sent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "sms_status", "displayName": "sms_status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reply_received", "displayName": "reply_received", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "notes", "displayName": "notes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_maps_url", "displayName": "google_maps_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "rating", "displayName": "rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "reviews_count", "displayName": "reviews_count", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "1e58b79c-92bf-44ce-8c72-a24081663a7a", "name": "Update SMS Failed Status2", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-3600, 2976], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Check SMS history and merge with lead data\nconst items = $input.all();\nconst processedItems = [];\n\n// Get Supabase credentials from environment\nconst supabaseUrl = $env.SUPABASE_URL;\nconst supabaseKey = $env.SUPABASE_ANON_KEY;\n\nif (!supabaseUrl || !supabaseKey) {\n  console.log('Supabase credentials not found - skipping SMS history check');\n  // Pass through all leads without SMS history check\n  for (const item of items) {\n    const leadData = item.json;\n    const enhancedLead = {\n      ...leadData,\n      sms_history_checked: false,\n      has_sms_history: false,\n      sms_history_details: null\n    };\n    processedItems.push({ json: enhancedLead });\n  }\n  return processedItems;\n}\n\nfor (const item of items) {\n  const leadData = item.json;\n  \n  try {\n    // Make API call to Supabase to check SMS history\n    const response = await fetch(`${supabaseUrl}/rest/v1/sms_tracking?normalized_business_name=eq.${encodeURIComponent(leadData.clean_business_name)}&phone_number=eq.${encodeURIComponent(leadData.clean_phone_number)}&limit=1`, {\n      method: 'GET',\n      headers: {\n        'apikey': supabaseKey,\n        'Authorization': `Bearer ${supabaseKey}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    if (!response.ok) {\n      throw new Error(`Supabase API error: ${response.status}`);\n    }\n    \n    const smsHistory = await response.json();\n    const hasSmsHistory = Array.isArray(smsHistory) && smsHistory.length > 0;\n    \n    // Always pass through the original lead data with SMS history info added\n    const enhancedLead = {\n      ...leadData,\n      sms_history_checked: true,\n      has_sms_history: hasSmsHistory,\n      sms_history_details: hasSmsHistory ? smsHistory[0] : null\n    };\n    \n    processedItems.push({ json: enhancedLead });\n    console.log(`Lead: ${leadData.clean_business_name} - SMS history: ${hasSmsHistory ? 'FOUND' : 'NOT FOUND'}`);\n    \n  } catch (error) {\n    console.log(`Error checking SMS history for ${leadData.clean_business_name}: ${error.message}`);\n    \n    // On error, assume no SMS history and continue\n    const enhancedLead = {\n      ...leadData,\n      sms_history_checked: false,\n      has_sms_history: false,\n      sms_history_details: null,\n      sms_check_error: error.message\n    };\n    \n    processedItems.push({ json: enhancedLead });\n  }\n}\n\nconsole.log(`Checked SMS history for ${processedItems.length} leads - all leads passed through`);\nreturn processedItems;"}, "id": "aabbde6a-60ea-4351-952d-be386a58f5b3", "name": "Check SMS History3", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4400, 2768]}, {"parameters": {"jsCode": "// Prepare leads for SMS history check by cleaning and normalizing data\nconst items = $input.all();\nconst processedItems = [];\n\n// Function to remove Lithuanian characters and normalize text\nfunction normalizeText(text) {\n  if (!text) return '';\n  \n  return text\n    .trim()\n    .replace(/ą/g, 'a')\n    .replace(/č/g, 'c')\n    .replace(/ę/g, 'e')\n    .replace(/ė/g, 'e')\n    .replace(/į/g, 'i')\n    .replace(/š/g, 's')\n    .replace(/ų/g, 'u')\n    .replace(/ū/g, 'u')\n    .replace(/ž/g, 'z')\n    .replace(/Ą/g, 'A')\n    .replace(/Č/g, 'C')\n    .replace(/Ę/g, 'E')\n    .replace(/Ė/g, 'E')\n    .replace(/Į/g, 'I')\n    .replace(/Š/g, 'S')\n    .replace(/Ų/g, 'U')\n    .replace(/Ū/g, 'U')\n    .replace(/Ž/g, 'Z')\n    .replace(/,/g, '') // Remove commas\n    .replace(/\\|/g, '') // Remove pipe characters\n    .replace(/[^a-zA-Z0-9\\s\\-]/g, '') // Remove other special characters (keeping only letters, numbers, spaces, hyphens)\n    .replace(/\\s+/g, ' ') // Normalize spaces\n    .trim();\n}\n\nfor (const item of items) {\n  const leadData = item.json;\n  \n  // Clean and normalize the data for comparison\n  const businessName = normalizeText(leadData.business_name);\n  const phoneNumber = (leadData.google_phone || leadData.owner_phone || '').replace(/\\s+/g, '').replace(/[^0-9+]/g, '');\n  \n  if (!businessName || !phoneNumber) {\n    console.log(`Skipping lead with missing data: ${businessName} / ${phoneNumber}`);\n    continue;\n  }\n  \n  // Add cleaned data to the lead for later use\n  const enhancedLead = {\n    ...leadData,\n    clean_business_name: businessName,\n    clean_phone_number: phoneNumber\n  };\n  \n  processedItems.push({ json: enhancedLead });\n}\n\nconsole.log(`Processed ${processedItems.length} leads for SMS history check`);\nreturn processedItems;"}, "id": "678518b7-2137-40ef-88f8-95f911d52c5b", "name": "Prepare Leads for Check2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4560, 2672]}, {"parameters": {"jsCode": "// Filter out leads that have already received SMS\nconst items = $input.all();\nconst filteredItems = [];\n\nfor (const item of items) {\n  const leadData = item.json;\n  \n  // Check if SMS history exists (currently always false, but ready for future enhancement)\n  const hasSmsHistory = leadData.has_sms_history === true;\n  \n  if (!hasSmsHistory) {\n    // No SMS history found, safe to send SMS\n    // Clean up the lead data and remove our temporary fields\n    const cleanLead = {\n      business_name: leadData.business_name,\n      address: leadData.address,\n      google_phone: leadData.google_phone,\n      owner_phone: leadData.owner_phone,\n      website: leadData.website,\n      phone_source: leadData.phone_source,\n      sms_sent: leadData.sms_sent,\n      sms_status: leadData.sms_status,\n      reply_received: leadData.reply_received,\n      notes: leadData.notes,\n      google_maps_url: leadData.google_maps_url,\n      category: leadData.category,\n      rating: leadData.rating,\n      reviews_count: leadData.reviews_count\n    };\n    \n    filteredItems.push({ json: cleanLead });\n    console.log(`Lead ${leadData.clean_business_name || leadData.business_name} - No SMS history, will send SMS`);\n  } else {\n    console.log(`Lead ${leadData.clean_business_name || leadData.business_name} - SMS already sent, skipping`);\n  }\n}\n\nconsole.log(`Filtered ${filteredItems.length} leads without SMS history from ${items.length} total leads`);\nreturn filteredItems;"}, "id": "afeba133-1ddb-4fe2-ba2d-04dce538e82d", "name": "<PERSON><PERSON> Unsent Leads2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4368, 3040]}, {"parameters": {"tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "normalized_business_name", "fieldValue": "={{ $json.business_name.trim().replace(/ą/g, 'a').replace(/č/g, 'c').replace(/ę/g, 'e').replace(/ė/g, 'e').replace(/į/g, 'i').replace(/š/g, 's').replace(/ų/g, 'u').replace(/ū/g, 'u').replace(/ž/g, 'z').replace(/Ą/g, 'A').replace(/Č/g, 'C').replace(/Ę/g, 'E').replace(/Ė/g, 'E').replace(/Į/g, 'I').replace(/Š/g, 'S').replace(/Ų/g, 'U').replace(/Ū/g, 'U').replace(/Ž/g, 'Z').replace(/,/g, '').replace(/\\|/g, '').replace(/[^a-zA-Z0-9\\s\\-]/g, '').replace(/\\s+/g, ' ').trim() }}"}, {"fieldId": "phone_number", "fieldValue": "={{ ($json.phone_used || $json.google_phone || $json.owner_phone || '').replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}, {"fieldId": "google_maps_url", "fieldValue": "={{ $json.google_maps_url }}"}, {"fieldId": "category", "fieldValue": "={{ $json.category }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_sent }}"}, {"fieldId": "error_message", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "phone_source", "fieldValue": "={{ $json.google_phone ? 'google_phone' : 'owner_phone' }}"}]}}, "id": "ab3658b2-aa52-4b7c-b9f9-2e1f18c70a40", "name": "Record SMS Success", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-3328, 2768], "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}, {"parameters": {"tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "normalized_business_name", "fieldValue": "={{ $json.business_name.trim().replace(/ą/g, 'a').replace(/č/g, 'c').replace(/ę/g, 'e').replace(/ė/g, 'e').replace(/į/g, 'i').replace(/š/g, 's').replace(/ų/g, 'u').replace(/ū/g, 'u').replace(/ž/g, 'z').replace(/Ą/g, 'A').replace(/Č/g, 'C').replace(/Ę/g, 'E').replace(/Ė/g, 'E').replace(/Į/g, 'I').replace(/Š/g, 'S').replace(/Ų/g, 'U').replace(/Ū/g, 'U').replace(/Ž/g, 'Z').replace(/,/g, '').replace(/\\|/g, '').replace(/[^a-zA-Z0-9\\s\\-]/g, '').replace(/\\s+/g, ' ').trim() }}"}, {"fieldId": "phone_number", "fieldValue": "={{ $json.phone_used.replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}, {"fieldId": "google_maps_url", "fieldValue": "={{ $json.google_maps_url }}"}, {"fieldId": "category", "fieldValue": "={{ $json.category }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_sent }}"}, {"fieldId": "error_message", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "phone_source", "fieldValue": "={{ $json.google_phone ? 'google_phone' : 'owner_phone' }}"}]}}, "id": "0a818f5b-cfc1-44c8-8a2f-6e26c462a53d", "name": "Record SMS Failed", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-3328, 2992], "credentials": {"supabaseApi": {"id": "UreeSglD1aITdAps", "name": "Supabase account"}}}], "pinData": {"When clicking ‘Execute workflow’": [{"json": {"query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "Lietuva"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Apify Google Maps Scraper3", "type": "main", "index": 0}]]}, "Apify Google Maps Scraper3": {"main": [[{"node": "Filter Businesses Without Website3", "type": "main", "index": 0}]]}, "Filter Businesses Without Website3": {"main": [[{"node": "Normalise reviews, and3", "type": "main", "index": 0}]]}, "Remove Duplicates3": {"main": [[{"node": "Filtered_Leads3", "type": "main", "index": 0}]]}, "Unfiltered_Leads3": {"main": [[{"node": "Remove Duplicates3", "type": "main", "index": 0}]]}, "Filtered_Leads3": {"main": [[{"node": "Prepare Leads for Check2", "type": "main", "index": 0}]]}, "Normalise reviews, and3": {"main": [[{"node": "Unfiltered_Leads3", "type": "main", "index": 0}]]}, "SMS11": {"main": [[{"node": "Process SMS Response2", "type": "main", "index": 0}]]}, "Process SMS Response2": {"main": [[{"node": "Check SMS Success2", "type": "main", "index": 0}]]}, "Check SMS Success2": {"main": [[{"node": "Update SMS Success Status2", "type": "main", "index": 0}], [{"node": "Update SMS Failed Status2", "type": "main", "index": 0}]]}, "Update SMS Success Status2": {"main": [[{"node": "Record SMS Success", "type": "main", "index": 0}]]}, "Update SMS Failed Status2": {"main": [[{"node": "Record SMS Failed", "type": "main", "index": 0}]]}, "Check SMS History3": {"main": [[{"node": "<PERSON><PERSON> Unsent Leads2", "type": "main", "index": 0}]]}, "Prepare Leads for Check2": {"main": [[{"node": "Check SMS History3", "type": "main", "index": 0}]]}, "Filter Unsent Leads2": {"main": [[{"node": "SMS11", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "82a511f2-0766-4e5c-81de-4bff12e3875e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e792ababaacd3609090c67925029f9ce56f8b2294355abafc6b7c9d2271c36bb"}, "id": "5JmQD48moWbfAuMi", "tags": []}