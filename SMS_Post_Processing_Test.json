{"name": "SMS Post-Processing Test Workflow", "nodes": [{"parameters": {"jsCode": "// Mock SMS8 response data for testing\n// This simulates the output from SMS8 node with both successful and failed SMS responses\nconst mockSmsResponses = [\n  {\n    \"business_name\": \"Test Flower Shop\",\n    \"address\": \"Vilnius, Lithuania\",\n    \"google_phone\": \"+***********\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://maps.google.com/test\",\n    \"category\": \"Flower shop\",\n    \"rating\": \"4.5\",\n    \"reviews_count\": \"25\",\n    \"sms_response\": {\n      \"success\": true,\n      \"status\": \"success\",\n      \"message\": \"SMS sent successfully\",\n      \"id\": \"sms_12345\"\n    }\n  },\n  {\n    \"business_name\": \"Test Restaurant\",\n    \"address\": \"Kaunas, Lithuania\",\n    \"google_phone\": \"+***********\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://maps.google.com/test2\",\n    \"category\": \"Restaurant\",\n    \"rating\": \"4.2\",\n    \"reviews_count\": \"18\",\n    \"sms_response\": {\n      \"success\": false,\n      \"error\": \"Invalid phone number\",\n      \"status\": \"failed\"\n    }\n  },\n  {\n    \"business_name\": \"Test Cafe\",\n    \"address\": \"Klaipėda, Lithuania\",\n    \"google_phone\": \"+***********\",\n    \"owner_phone\": \"\",\n    \"website\": \"\",\n    \"phone_source\": \"google\",\n    \"reply_received\": \"no\",\n    \"notes\": \"Lead from Google Maps - no website found\",\n    \"google_maps_url\": \"https://maps.google.com/test3\",\n    \"category\": \"Cafe\",\n    \"rating\": \"4.8\",\n    \"reviews_count\": \"42\",\n    \"sms_response\": {\n      \"success\": true,\n      \"message\": \"Message delivered\",\n      \"code\": 200\n    }\n  }\n];\n\nreturn mockSmsResponses.map(data => ({ json: data }));"}, "id": "mock-sms8-data-node-id", "name": "Mock SMS8 Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [100, 100]}, {"parameters": {"jsCode": "// Process SMS response and prepare data for Google Sheets update\nconst items = $input.all();\nconst processedItems = [];\n\nfor (const item of items) {\n  // Now the SMS response is in sms_response property, and original data is preserved\n  const originalData = item.json;\n  const smsResponse = item.json.sms_response || item.json; // Fallback to full json if sms_response not found\n  \n  // Determine SMS status based on response\n  let smsStatus = 'failed';\n  let smsStatusDetails = '';\n  \n  // Check if SMS was sent successfully\n  // SMS8 typically returns success: true/false or status codes\n  if (smsResponse.success === true || smsResponse.status === 'success' || smsResponse.code === 200) {\n    smsStatus = 'success';\n    smsStatusDetails = 'SMS sent successfully';\n  } else if (smsResponse.success === false || smsResponse.error) {\n    smsStatus = 'failed';\n    smsStatusDetails = smsResponse.error || smsResponse.message || 'SMS sending failed';\n  } else {\n    // If response structure is different, check for common success indicators\n    const responseStr = JSON.stringify(smsResponse).toLowerCase();\n    if (responseStr.includes('success') || responseStr.includes('sent')) {\n      smsStatus = 'success';\n      smsStatusDetails = 'SMS sent successfully';\n    } else {\n      smsStatus = 'failed';\n      smsStatusDetails = 'Unknown SMS response: ' + JSON.stringify(smsResponse);\n    }\n  }\n  \n  // Prepare updated data - now we have access to the original business data\n  const updatedData = {\n    business_name: originalData.business_name,\n    address: originalData.address,\n    google_phone: originalData.google_phone,\n    owner_phone: originalData.owner_phone,\n    website: originalData.website,\n    phone_source: originalData.phone_source,\n    reply_received: originalData.reply_received,\n    notes: originalData.notes,\n    google_maps_url: originalData.google_maps_url,\n    category: originalData.category,\n    rating: originalData.rating,\n    reviews_count: originalData.reviews_count,\n    sms_sent: smsStatus,\n    sms_status: smsStatusDetails,\n    sms_sent_date: new Date().toISOString().split('T')[0],\n    phone_used: originalData.google_phone || originalData.owner_phone || 'no_phone',\n    sms_response_raw: JSON.stringify(smsResponse) // Keep raw response for debugging\n  };\n  \n  processedItems.push({ json: updatedData });\n}\n\nconsole.log(`Processed ${processedItems.length} SMS responses`);\nreturn processedItems;"}, "id": "f1a2b3c4-d5e6-f7g8-h9i0-j1k2l3m4n5o6", "name": "Process SMS Response2", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [340, 100]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition1", "leftValue": "={{ $json.sms_sent }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, "id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "name": "Check SMS Success2", "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [580, 100]}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name || $json.title || 'Unknown Business' }}", "address": "={{ $json.address || '' }}", "google_phone": "={{ $json.google_phone || '' }}", "owner_phone": "={{ $json.owner_phone || '' }}", "website": "={{ $json.website || '' }}", "phone_source": "={{ $json.phone_source || '' }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "sms_sent_date": "={{ $json.sms_sent_date }}", "phone_used": "={{ $json.phone_used }}", "reply_received": "={{ $json.reply_received || 'no' }}", "notes": "={{ $json.notes || '' }}", "google_maps_url": "={{ $json.google_maps_url || '' }}", "category": "={{ $json.category || '' }}", "rating": "={{ $json.rating || '' }}", "reviews_count": "={{ $json.reviews_count || '' }}"}, "matchingColumns": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}]}, "options": {}}, "id": "b2c3d4e5-f6g7-h8i9-j0k1-l2m3n4o5p6q7", "name": "Update SMS Success Status2", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [820, 20], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nE2TryBqJyb5j9euNlSKgCCnMmjW2IITo3PQgDFUEWw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name || $json.title || 'Unknown Business' }}", "address": "={{ $json.address || '' }}", "google_phone": "={{ $json.google_phone || '' }}", "owner_phone": "={{ $json.owner_phone || '' }}", "website": "={{ $json.website || '' }}", "phone_source": "={{ $json.phone_source || '' }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "sms_sent_date": "={{ $json.sms_sent_date }}", "phone_used": "={{ $json.phone_used }}", "reply_received": "={{ $json.reply_received || 'no' }}", "notes": "={{ $json.notes || '' }}", "google_maps_url": "={{ $json.google_maps_url || '' }}", "category": "={{ $json.category || '' }}", "rating": "={{ $json.rating || '' }}", "reviews_count": "={{ $json.reviews_count || '' }}"}, "matchingColumns": [{"id": "business_name", "displayName": "business_name", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "google_phone", "displayName": "google_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}]}, "options": {}}, "id": "c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8", "name": "Update SMS Failed Status2", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [820, 180], "credentials": {"googleSheetsOAuth2Api": {"id": "oKljuM10DCL4C6sO", "name": "Google Sheets account"}}}, {"parameters": {"resource": "row", "operation": "create", "tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "normalized_business_name", "fieldValue": "={{ $json.business_name.trim().replace(/ą/g, 'a').replace(/č/g, 'c').replace(/ę/g, 'e').replace(/ė/g, 'e').replace(/į/g, 'i').replace(/š/g, 's').replace(/ų/g, 'u').replace(/ū/g, 'u').replace(/ž/g, 'z').replace(/Ą/g, 'A').replace(/Č/g, 'C').replace(/Ę/g, 'E').replace(/Ė/g, 'E').replace(/Į/g, 'I').replace(/Š/g, 'S').replace(/Ų/g, 'U').replace(/Ū/g, 'U').replace(/Ž/g, 'Z').replace(/,/g, '').replace(/\\|/g, '').replace(/[^a-zA-Z0-9\\s\\-]/g, '').replace(/\\s+/g, ' ').trim() }}"}, {"fieldId": "phone_number", "fieldValue": "={{ $json.phone_used.replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}, {"fieldId": "google_maps_url", "fieldValue": "={{ $json.google_maps_url }}"}, {"fieldId": "category", "fieldValue": "={{ $json.category }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_sent }}"}, {"fieldId": "error_message", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "phone_source", "fieldValue": "={{ $json.google_phone ? 'google_phone' : 'owner_phone' }}"}]}}, "id": "record-sms-success-node-id", "name": "Record SMS Success2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1060, 20], "credentials": {"supabaseApi": {"id": "your-supabase-credentials-id", "name": "Supabase account"}}}, {"parameters": {"resource": "row", "operation": "create", "tableId": "sms_tracking", "fieldsUi": {"fieldValues": [{"fieldId": "business_name", "fieldValue": "={{ $json.business_name }}"}, {"fieldId": "normalized_business_name", "fieldValue": "={{ $json.business_name.trim().replace(/ą/g, 'a').replace(/č/g, 'c').replace(/ę/g, 'e').replace(/ė/g, 'e').replace(/į/g, 'i').replace(/š/g, 's').replace(/ų/g, 'u').replace(/ū/g, 'u').replace(/ž/g, 'z').replace(/Ą/g, 'A').replace(/Č/g, 'C').replace(/Ę/g, 'E').replace(/Ė/g, 'E').replace(/Į/g, 'I').replace(/Š/g, 'S').replace(/Ų/g, 'U').replace(/Ū/g, 'U').replace(/Ž/g, 'Z').replace(/,/g, '').replace(/\\|/g, '').replace(/[^a-zA-Z0-9\\s\\-]/g, '').replace(/\\s+/g, ' ').trim() }}"}, {"fieldId": "phone_number", "fieldValue": "={{ $json.phone_used.replace(/\\s+/g, '').replace(/[^0-9+]/g, '') }}"}, {"fieldId": "address", "fieldValue": "={{ $json.address }}"}, {"fieldId": "google_maps_url", "fieldValue": "={{ $json.google_maps_url }}"}, {"fieldId": "category", "fieldValue": "={{ $json.category }}"}, {"fieldId": "sms_status", "fieldValue": "={{ $json.sms_sent }}"}, {"fieldId": "error_message", "fieldValue": "={{ $json.sms_status }}"}, {"fieldId": "phone_source", "fieldValue": "={{ $json.google_phone ? 'google_phone' : 'owner_phone' }}"}]}}, "id": "record-sms-failed-node-id", "name": "Record SMS Failed2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1060, 180], "credentials": {"supabaseApi": {"id": "your-supabase-credentials-id", "name": "Supabase account"}}}], "connections": {"Mock SMS8 Data": {"main": [[{"node": "Process SMS Response2", "type": "main", "index": 0}]]}, "Process SMS Response2": {"main": [[{"node": "Check SMS Success2", "type": "main", "index": 0}]]}, "Check SMS Success2": {"main": [[{"node": "Update SMS Success Status2", "type": "main", "index": 0}], [{"node": "Update SMS Failed Status2", "type": "main", "index": 0}]]}, "Update SMS Success Status2": {"main": [[{"node": "Record SMS Success2", "type": "main", "index": 0}]]}, "Update SMS Failed Status2": {"main": [[{"node": "Record SMS Failed2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}}